import Navigation from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { getReq, postReq } from '@/components/APis/ApiService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Type definitions
interface Organization {
  id: number;
  name: string;
  address: string;
  logo: string | null;
  contact_number: string;
  email: string;
  pan_no: string;
  reg_no: string;
  description: string;
}

interface Service {
  id: number;
  name: string;
  description: string;
  offer: string;
  process: string;
}

interface MessageData {
  full_name: string;
  email: string;
  contact_number: string;
  service: number;
  message: string;
}

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });

  const [services, setServices] = useState<Service[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [servicesLoading, setServicesLoading] = useState(true);

  // Fetch services on component mount
  useEffect(() => {
    fetchServices();
    fetchOrganizations();
  }, []);

  const fetchServices = async () => {
    try {
      setServicesLoading(true);
      const data = await getReq('services/');
      if (data) {
        setServices(Array.isArray(data) ? data : [data]);
        
      } else {
        toast.error('Failed to load services. Please refresh the page.');
      }
    } catch (error: any) {
  
      toast.error('Failed to load services. Please refresh the page.');
    } finally {
      setServicesLoading(false);
    }
  };

  const fetchOrganizations = async () => {
    try {
      const data = await getReq('organizations/');
      if (data) {
        setOrganizations(Array.isArray(data) ? data : [data]);
       
      } else {
        
      }
    } catch (error: any) {
      
      // Don't show toast for organizations as it's not critical for form functionality
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.phone || !formData.service) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);

    try {
      const messageData: MessageData = {
        full_name: formData.name,
        email: formData.email,
        contact_number: formData.phone,
        service: parseInt(formData.service),
        message: formData.message
      };

    
      const response = await postReq('send-messages/', messageData);
      
      
      
      if (response && response.status === "201" || response.status === "200") {
        // Reset form on success
        setFormData({
          name: '',
          email: '',
          phone: '',
          service: '',
          message: ''
        });
        toast.success('Message sent successfully! We will get back to you soon.');
      } else {
        // Handle case where API doesn't return success but doesn't throw error
        toast.error('Failed to send message. Please try again later.');
      }
    } catch (error: any) {
  
      toast.error(error.message || 'An error occurred while sending your message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <Navigation />
      
      <div className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                Contact Us
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Get in touch with our expert counselors today
              </p>
            </div>
          </div>
        </section>

        {/* Contact Content */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Send us a Message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Full Name</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Email Address</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
                      placeholder="+91 9876543210"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Service Interested In</label>
                    <select
                      name="service"
                      value={formData.service}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
                      required
                      disabled={servicesLoading}
                    >
                      <option value="" className="text-gray-500 dark:text-gray-400">
                        {servicesLoading ? "Loading services..." : "Select a service"}
                      </option>
                      {services.map((service) => (
                        <option 
                          key={service.id} 
                          value={service.id.toString()} 
                          className="text-gray-900 dark:text-white"
                        >
                          {service.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 dark:text-gray-300 mb-2 font-medium">Message</label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors resize-vertical"
                      placeholder="Tell us about your study abroad goals..."
                    />
                  </div>
                  
                  <Button 
                    type="submit" 
                    size="lg" 
                    disabled={loading || servicesLoading}
                    className="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading ? 'Sending...' : 'Send Message'}
                  </Button>
                </form>
              </div>

              {/* Contact Information */}
              <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg dark:shadow-gray-900/20 border border-gray-200 dark:border-gray-700">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Get in Touch</h2>
                
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="bg-primary/10 dark:bg-primary/20 p-3 rounded-lg mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-primary dark:text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">Email</h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        {organizations.length > 0 ? organizations[0].email : '<EMAIL>'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="bg-primary/10 dark:bg-primary/20 p-3 rounded-lg mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-primary dark:text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">Phone</h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        {organizations.length > 0 ? organizations[0].contact_number : '+91 98765 43210'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="bg-primary/10 dark:bg-primary/20 p-3 rounded-lg mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-primary dark:text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">Address</h3>
                      <p className="text-gray-600 dark:text-gray-300">
                        {organizations.length > 0 ? organizations[0].address : '123 Education Street\nCity Center, State 12345'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="bg-primary/10 dark:bg-primary/20 p-3 rounded-lg mr-4 flex-shrink-0">
                      <svg className="w-6 h-6 text-primary dark:text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">Office Hours</h3>
                      <p className="text-gray-600 dark:text-gray-300">Monday - Friday: 9 AM - 7 PM<br/>Saturday: 10 AM - 4 PM</p>
                    </div>
                  </div>
                </div>

                {/* Quick Action Buttons */}
                <div className="mt-8 space-y-3">
                  <Button 
                    onClick={() => {
                      const phone = organizations.length > 0 ? organizations[0].contact_number : '919876543210';
                      window.open(`https://wa.me/${phone}?text=Hi, I would like to know more about study abroad opportunities.`, '_blank');
                    }}
                    className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 transform hover:scale-105 transition-all duration-200"
                  >
                    WhatsApp Us
                  </Button>
                  <Button 
                    onClick={() => {
                      const phone = organizations.length > 0 ? organizations[0].contact_number : '+919876543210';
                      window.open(`tel:${phone}`, '_self');
                    }}
                    variant="outline"
                    className="w-full border-primary text-primary hover:bg-primary hover:text-white dark:border-primary dark:text-primary dark:hover:bg-primary dark:hover:text-white font-medium py-3 transform hover:scale-105 transition-all duration-200"
                  >
                    Call Now
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-8 border-t border-gray-800 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p>&copy; 2024 {organizations.length > 0 ? organizations[0].name : 'NextGen Hub Pvt Ltd'}. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default Contact;