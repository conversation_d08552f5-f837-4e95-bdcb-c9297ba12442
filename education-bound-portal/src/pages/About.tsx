import { useEffect, useState } from 'react';
import Navigation from '@/components/Navigation';
import { getReq, getExperiences } from '@/components/APis/ApiService';
import TestimonialsSection from '@/components/TestimonialsSection';

interface Position {
  id: number;
  name: string;
}

interface TeamMember {
  id: number;
  first_name: string;
  middle_name?: string;
  last_name: string;
  image: string;
  position: Position;
  experience: string;
  uploaded_at: string;
}

interface AboutVideo {
  id: number;
  about: number;
  video: string | null;
  order: number;
  uploaded_at: string;
}

interface Experience {
  id: number;
  title: string;
  about: string | null;
  experience: string[] | null;
  image: string | null;
  uploaded_at: string;
}

interface AboutData {
  id: number;
  story: string;
  mission: string[] | null;
  vision: string[] | null;
  image: string | null;
  videos: AboutVideo[];
  uploaded_at: string;
}

const About = () => {
  const [about, setAbout] = useState<AboutData | null>(null);
  const [team, setTeam] = useState<TeamMember[]>([]);
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [teamLoading, setTeamLoading] = useState<boolean>(true);
  const [experienceLoading, setExperienceLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [teamError, setTeamError] = useState<string | null>(null);
  const [experienceError, setExperienceError] = useState<string | null>(null);
  const [currentVideoIndex, setCurrentVideoIndex] = useState<number>(0);
  const [showVideo, setShowVideo] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(true);
  const [isMuted, setIsMuted] = useState<boolean>(true);
  const [showControls, setShowControls] = useState<boolean>(false);

  useEffect(() => {
   
    setLoading(true);
    
    // Fetch about data
    getReq('about/')
      .then((data) => {
     
        if (Array.isArray(data) && data.length > 0) {
          setAbout(data[0]);
        } else if (data === null) {
         
          setError('Failed to load data. API returned null.');
        } else {
         
          setError('No about information found.');
          setAbout(null);
        }
      })
      .catch((err) => {
       
        setError('Failed to load about information.');
      })
      .finally(() => {
        setLoading(false);
      
      });

    // Fetch team data
    setTeamLoading(true);
    getReq('team/')
      .then((data) => {
       
        if (Array.isArray(data)) {
          setTeam(data);
        } else if (data === null) {
         
          setTeamError('Failed to load team data. API returned null.');
        } else {
         
          setTeamError('No team information found.');
          setTeam([]);
        }
      })
      .catch((err) => {
       
        setTeamError('Failed to load team information.');
      })
      .finally(() => {
        setTeamLoading(false);

      });

    // Fetch experience data
    setExperienceLoading(true);
    getExperiences()
      .then((data) => {
        if (Array.isArray(data)) {
          setExperiences(data);
        } else {
         
          setExperienceError('No experience information found.');
          setExperiences([]);
        }
      })
      .catch((err) => {
       
        setExperienceError('Failed to load experience information.');
      })
      .finally(() => {
        setExperienceLoading(false);
      });
  }, []);

  // Video slideshow effect
  useEffect(() => {
    if (!about?.videos || about.videos.length === 0) return;

    // Start with image for 3 seconds, then switch to videos
    const initialTimer = setTimeout(() => {
      setShowVideo(true);
    }, 3000);

    return () => clearTimeout(initialTimer);
  }, [about]);

  useEffect(() => {
    if (!about?.videos || about.videos.length <= 1 || !showVideo) return;

    const interval = setInterval(() => {
      setCurrentVideoIndex((prevIndex) =>
        (prevIndex + 1) % about.videos.length
      );
    }, 8000); // Switch video every 8 seconds

    return () => clearInterval(interval);
  }, [about?.videos, showVideo]);

  const getFullName = (member: TeamMember) => {
    const parts = [member.first_name, member.middle_name, member.last_name].filter(Boolean);
    return parts.join(' ');
  };

  const getTeamImageUrl = (imageUrl: string) => {
    // If the image URL is already a full URL, use it as is
    // If it's a relative path, it should work with your domain
    return imageUrl || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face";
  };

  const getVideoUrl = (videoUrl: string) => {
    // If the video URL is already a full URL, use it as is
    // If it's a relative path, it should work with your domain
    return videoUrl;
  };

  const togglePlayPause = () => {
    const videos = document.querySelectorAll('.story-video');
    videos.forEach((video) => {
      const videoElement = video as HTMLVideoElement;
      if (isPlaying) {
        videoElement.pause();
      } else {
        videoElement.play();
      }
    });
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    const videos = document.querySelectorAll('.story-video');
    videos.forEach((video) => {
      const videoElement = video as HTMLVideoElement;
      videoElement.muted = !isMuted;
    });
    setIsMuted(!isMuted);
  };

  const nextVideo = () => {
    if (about?.videos && about.videos.length > 1) {
      setCurrentVideoIndex((prevIndex) =>
        (prevIndex + 1) % about.videos.length
      );
    }
  };

  const prevVideo = () => {
    if (about?.videos && about.videos.length > 1) {
      setCurrentVideoIndex((prevIndex) =>
        prevIndex === 0 ? about.videos.length - 1 : prevIndex - 1
      );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 text-center">
          <p className="text-xl text-gray-600 dark:text-gray-300">Loading about information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 text-center">
          <p className="text-xl text-red-600 dark:text-red-400">Error: {error}</p>
        </div>
      </div>
    );
  }

  if (!about) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 text-center">
          <p className="text-xl text-gray-600 dark:text-gray-300">No about information available.</p>
        </div>
      </div>
    );
  }

  const storyImageUrl = about.image 
    ? about.image 
    : "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=600&h=400&fit=crop";
  const storyImageAlt = about.image ? "Our Story - NextGen Hub" : "Students studying - Placeholder";

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <Navigation />
      
      <div className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
               About US
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Your trusted partner in achieving international education dreams
              </p>
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Our Story</h2>
                <div className="text-gray-600 dark:text-gray-300 mb-4" dangerouslySetInnerHTML={{ __html: about.story }} />
              </div>
              <div className="relative">
                {/* Image/Video Container with smooth transitions */}
                <div
                  className="relative rounded-lg shadow-lg overflow-hidden bg-gray-200 dark:bg-gray-700"
                  onMouseEnter={() => setShowControls(true)}
                  onMouseLeave={() => setShowControls(false)}
                >
                  {/* About Image */}
                  <div
                    className={`transition-opacity duration-1000 ${showVideo ? 'opacity-0 absolute inset-0' : 'opacity-100'}`}
                  >
                    <img
                      src={storyImageUrl}
                      alt={storyImageAlt}
                      className="w-full h-auto object-cover"
                    />
                  </div>

                  {/* Video Slideshow */}
                  {about.videos && about.videos.length > 0 && (
                    <div
                      className={`transition-opacity duration-1000 ${showVideo ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}
                    >
                      {about.videos
                        .sort((a, b) => a.order - b.order)
                        .map((video, index) => (
                          <div
                            key={video.id}
                            className={`transition-opacity duration-1000 ${
                              index === currentVideoIndex ? 'opacity-100' : 'opacity-0 absolute inset-0'
                            }`}
                          >
                            {video.video && (
                              <video
                                className="story-video w-full h-auto object-cover"
                                style={{ aspectRatio: 'auto' }}
                                autoPlay={isPlaying}
                                muted={isMuted}
                                loop
                              >
                                <source src={getVideoUrl(video.video)} type="video/mp4" />
                                Your browser does not support the video tag.
                              </video>
                            )}
                          </div>
                        ))}
                    </div>
                  )}

                  {/* Video Controls */}
                  {showVideo && (
                    <div className={`absolute inset-0 bg-black/20 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
                      {/* Play/Pause Button */}
                      <button
                        onClick={togglePlayPause}
                        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-4 transition-all duration-200"
                      >
                        {isPlaying ? (
                          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                          </svg>
                        )}
                      </button>

                      {/* Navigation Arrows */}
                      {about.videos && about.videos.length > 1 && (
                        <>
                          <button
                            onClick={prevVideo}
                            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-200"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                          <button
                            onClick={nextVideo}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-200"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </>
                      )}

                      {/* Bottom Controls */}
                      <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
                        {/* Mute Button */}
                        <button
                          onClick={toggleMute}
                          className="bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-all duration-200"
                        >
                          {isMuted ? (
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12 8a1 1 0 011.414 0L15 9.586l1.293-1.293a1 1 0 111.414 1.414L16.414 11l1.293 1.293a1 1 0 01-1.414 1.414L15 12.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 11l-1.293-1.293A1 1 0 0112 8z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>

                        {/* Video indicators */}
                        {about.videos && about.videos.length > 1 && (
                          <div className="flex space-x-2">
                            {about.videos.map((_, index) => (
                              <button
                                key={index}
                                onClick={() => setCurrentVideoIndex(index)}
                                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                                  index === currentVideoIndex
                                    ? 'bg-white'
                                    : 'bg-white/50 hover:bg-white/70'
                                }`}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>



        {/* Mission & Vision */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 gap-12">
              <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Our Mission</h3>
                {about.mission && about.mission.length > 0 ? (
                  <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                    {about.mission.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-primary mr-2 mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-600 dark:text-gray-300">No mission statement available.</p>
                )}
              </div>
              <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Our Vision</h3>
                {about.vision && about.vision.length > 0 ? (
                  <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                    {about.vision.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-primary mr-2 mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-600 dark:text-gray-300">No vision statement available.</p>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Meet Our Team
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Experienced professionals dedicated to your success
              </p>
            </div>
            
            {teamLoading ? (
              <div className="text-center">
                <p className="text-lg text-gray-600 dark:text-gray-300">Loading team information...</p>
              </div>
            ) : teamError ? (
              <div className="text-center">
                <p className="text-lg text-red-600 dark:text-red-400">Error loading team: {teamError}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Showing placeholder team members
                </p>
                {/* Fallback to placeholder team */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
                  {[
                    { name: "Dr. Sarah Johnson", role: "Chief Education Counselor", experience: "15+ years" },
                    { name: "Michael Chen", role: "Visa Processing Expert", experience: "12+ years" },
                    { name: "Priya Sharma", role: "Test Preparation Specialist", experience: "10+ years" }
                  ].map((member, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                      <div className="text-center">
                        <div className="w-32 h-32 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <span className="text-2xl font-bold text-primary">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">{member.name}</h4>
                        <p className="text-primary font-medium mb-2">{member.role}</p>
                        <p className="text-gray-600 dark:text-gray-400">{member.experience}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : team.length === 0 ? (
              <div className="text-center">
                <p className="text-lg text-gray-600 dark:text-gray-300">No team members found.</p>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {team.map((member) => (
                  <div key={member.id} className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <div className="text-center">
                      <div className="relative mb-6">
                        <img 
                          src={getTeamImageUrl(member.image)}
                          alt={getFullName(member)}
                          className="w-32 h-32 rounded-full mx-auto object-cover border-4 border-primary/20 shadow-lg"
                          loading="lazy"
                        />
                        <div className="absolute -bottom-2 -right-2 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                          ✓
                        </div>
                      </div>
                      <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        {getFullName(member)}
                      </h4>
                      <p className="text-primary font-medium mb-2">
                        {member.position.name}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {member.experience} experience
                      </p>
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Experience Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800 transition-colors">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Our Experience & Expertise
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Years of dedicated service in international education
              </p>
            </div>

            {experienceLoading ? (
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-300">Loading experience information...</p>
              </div>
            ) : experienceError ? (
              <div className="text-center text-red-600 dark:text-red-400">
                <p>{experienceError}</p>
              </div>
            ) : experiences.length > 0 ? (
              <div className="space-y-12">
                {experiences.map((experience, index) => (
                  <div
                    key={experience.id}
                    className={`grid md:grid-cols-2 gap-8 items-center ${
                      index % 2 === 1 ? 'md:grid-flow-col-dense' : ''
                    }`}
                  >
                    <div className={index % 2 === 1 ? 'md:col-start-2' : ''}>
                      <h3 className="text-2xl font-bold text-primary mb-4">
                        {experience.title}
                      </h3>
                      {experience.about && (
                        <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                          {experience.about}
                        </p>
                      )}
                      {experience.experience && experience.experience.length > 0 && (
                        <ul className="space-y-3">
                          {experience.experience.map((item, idx) => (
                            <li key={idx} className="flex items-start">
                              <svg className="w-5 h-5 mr-3 mt-0.5 flex-shrink-0 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                              </svg>
                              <span className="text-gray-600 dark:text-gray-300">{item}</span>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                    <div className={index % 2 === 1 ? 'md:col-start-1' : ''}>
                      {experience.image ? (
                        <img
                          src={experience.image}
                          alt={experience.title}
                          className="rounded-lg shadow-lg w-full h-64 object-cover"
                        />
                      ) : (
                        <div className="bg-gradient-to-br from-primary/20 to-accent/20 rounded-lg h-64 flex items-center justify-center">
                          <span className="text-primary text-lg font-medium">Experience Image</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-600 dark:text-gray-300">
                <p>No experience information available at the moment.</p>
              </div>
            )}
          </div>
        </section>
      </div>
      {/* Testimonial section  */}
      <TestimonialsSection />

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-8 border-t border-gray-800 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p>&copy; 2024 NextGen Hub Pvt Ltd. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default About;