import { getTestimonials, getRatingNumber, getMediaUrl } from './APis/ApiService';
import { useEffect, useState } from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import type { Testimonial } from '@/types/api';

interface ProcessedTestimonial {
  id: number;
  name: string;
  university: string;
  country: string;
  rating: number;
  content: string;
  image: string;
}

const fetchTestimonials = async (): Promise<ProcessedTestimonial[]> => {
  try {
    const response = await getTestimonials();
    if (Array.isArray(response)) {
      return response.map((item) => ({
        id: item.id,
        name: item.name,
        university: item.uni,
        country: item.location,
        rating: getRatingNumber(item.rating),
        content: item.message,
        image: getMediaUrl(item.img) || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face&seed=${item.id}`
      }));
    }
    return [];
  } catch (error) {
    
    return [];
  }
};

const TestimonialsSection = () => {
  const [testimonials, setTestimonials] = useState<ProcessedTestimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTestimonials = async () => {
      try {
        setLoading(true);
        const data = await fetchTestimonials();
        setTestimonials(data);
      } catch (err) {
        setError('Failed to load testimonials');
       
      } finally {
        setLoading(false);
      }
    };

    loadTestimonials();
  }, []);

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <svg key={i} className={`w-6 h-6 ${i < rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`} fill="currentColor" viewBox="0 0 20 20">
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
      </svg>
    ));
  };

  // Calculate average rating from fetched testimonials
  const calculateAverageRating = (): number => {
    if (testimonials.length === 0) return 0;
    const sum = testimonials.reduce((acc, testimonial) => acc + testimonial.rating, 0);
    return Math.round((sum / testimonials.length) * 10) / 10; // Round to 1 decimal place
  };

  if (loading) {
    return (
      <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/30 transition-colors">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <p className="mt-6 text-lg text-gray-600 dark:text-gray-300">Loading testimonials...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/30 transition-colors">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-600 dark:text-red-400 text-lg">{error}</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (testimonials.length === 0) {
    return (
      <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/30 transition-colors">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 max-w-md mx-auto">
              <p className="text-gray-600 dark:text-gray-300 text-lg">No testimonials available at the moment.</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  const averageRating = calculateAverageRating();

  return (
    <section className="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/30 transition-colors overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-block mb-4">
            <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase">
              Student Success Stories
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
            What Our Students
            <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Say About Us
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Real success stories from students who achieved their study abroad dreams with our guidance
          </p>
        </div>

        {/* Main Carousel */}
        <div className="relative max-w-5xl mx-auto mb-20">
          <Carousel
            opts={{
              align: "center",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="ml-0">
              {testimonials.map((testimonial) => (
                <CarouselItem key={testimonial.id} className="pl-0 basis-full">
                  <div className="relative">
                    {/* Background decoration */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5 rounded-3xl transform rotate-1"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 to-purple-600/5 rounded-3xl transform -rotate-1"></div>
                    
                    {/* Main card */}
                    <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-3xl p-8 md:p-12 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-[1.02]">
                      {/* Quote icon */}
                      <div className="absolute top-8 right-8 text-blue-100 dark:text-blue-900/30">
                        <svg className="w-16 h-16 md:w-20 md:h-20" fill="currentColor" viewBox="0 0 32 32">
                          <path d="M10 8c-3.3 0-6 2.7-6 6v10h8V14h-4c0-1.1.9-2 2-2V8zm12 0c-3.3 0-6 2.7-6 6v10h8V14h-4c0-1.1.9-2 2-2V8z"/>
                        </svg>
                      </div>

                      {/* Content */}
                      <div className="relative z-10">
                        {/* Rating */}
                        <div className="flex justify-center mb-8">
                          <div className="flex space-x-1">
                            {renderStars(testimonial.rating)}
                          </div>
                        </div>

                        {/* Testimonial text */}
                        <blockquote className="text-center text-xl md:text-2xl lg:text-3xl text-gray-700 dark:text-gray-200 font-medium leading-relaxed mb-12 italic">
                          "{testimonial.content}"
                        </blockquote>

                        {/* Author info */}
                        <div className="flex flex-col items-center space-y-4">
                          <div className="relative">
                            <img 
                              src={testimonial.image}
                              alt={testimonial.name}
                              className="w-20 h-20 md:w-24 md:h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
                              onError={(e) => {
                                e.currentTarget.src = `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face&seed=${testimonial.id}`;
                              }}
                            />
                            <div className="absolute -bottom-2 -right-2 bg-green-500 w-8 h-8 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                              </svg>
                            </div>
                          </div>
                          
                          <div className="text-center">
                            <h4 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-1">
                              {testimonial.name}
                            </h4>
                            <p className="text-lg text-blue-600 dark:text-blue-400 font-semibold mb-1">
                              {testimonial.university}
                            </p>
                            <p className="text-gray-500 dark:text-gray-400 flex items-center justify-center">
                              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                              </svg>
                              {testimonial.country}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            
            {/* Custom navigation */}
            <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:bg-white dark:hover:bg-gray-800 w-12 h-12 shadow-lg" />
            <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:bg-white dark:hover:bg-gray-800 w-12 h-12 shadow-lg" />
          </Carousel>

          {/* Pagination dots */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <div key={index} className="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600"></div>
            ))}
          </div>
        </div>

        {/* Stats section */}
        <div className="text-center">
          <div className="inline-flex flex-wrap items-center justify-center gap-8 md:gap-16 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-white/20 dark:border-gray-700/50">
            <div className="text-center group">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                {averageRating}
              </div>
              <div className="text-sm md:text-base text-gray-600 dark:text-gray-300 font-medium">Average Rating</div>
              <div className="flex justify-center mt-2">
                {renderStars(Math.round(averageRating))}
              </div>
            </div>
            
            <div className="h-12 w-px bg-gray-300 dark:bg-gray-600 hidden md:block"></div>
            
            <div className="text-center group">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                {testimonials.length}+
              </div>
              <div className="text-sm md:text-base text-gray-600 dark:text-gray-300 font-medium">Happy Students</div>
            </div>
            
            <div className="h-12 w-px bg-gray-300 dark:bg-gray-600 hidden md:block"></div>
            
            <div className="text-center group">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform">
                95%
              </div>
              <div className="text-sm md:text-base text-gray-600 dark:text-gray-300 font-medium">Success Rate</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;