import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { MessageCircle } from 'lucide-react';
import { getReq, postReq } from '@/components/APis/ApiService';

interface Service {
  id: number;
  name: string;
  description: string;
  offer: string;
  process: string;
}

interface Organization {
  id: number;
  name: string;
  address: string;
  logo: string | null;
  contact_number: string;
  email: string;
  pan_no: string;
  reg_no: string;
  description: string;
}

const ContactSection = () => {
  const [contactForm, setContactForm] = useState({
    full_name: '',
    email: '',
    contact_number: '',
    service: '',
    message: ''
  });
  
  const [services, setServices] = useState<Service[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const { toast } = useToast();

  // Fetch services and organization data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch services
        const servicesData = await getReq('services/');
        if (servicesData && Array.isArray(servicesData)) {
          setServices(servicesData);
        }
        
        // Fetch organization data
        const organizationData = await getReq('organizations/');
        if (organizationData && Array.isArray(organizationData) && organizationData.length > 0) {
          setOrganization(organizationData[0]); // Use first organization
        }
        
      } catch (error) {
       
        toast({
          title: "Error",
          description: "Failed to load services data. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!contactForm.full_name || !contactForm.email || !contactForm.message) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await postReq('send-messages/', {
        full_name: contactForm.full_name,
        email: contactForm.email,
        contact_number: contactForm.contact_number,
        service: contactForm.service ? parseInt(contactForm.service) : null,
        message: contactForm.message
      });
      
      if (response && response.status === "200") {
        toast({
          title: "Message Sent!",
          description: "Thank you for contacting us. We'll get back to you within 24 hours.",
        });
        
        // Reset form
        setContactForm({
          full_name: '',
          email: '',
          contact_number: '',
          service: '',
          message: ''
        });
      }
    } catch (error) {
      
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setContactForm({
      ...contactForm,
      [e.target.name]: e.target.value
    });
  };

  const handleCallClick = () => {
    const phoneNumber = organization?.contact_number || '+919876543210';
    window.open(`tel:${phoneNumber}`, '_self');
  };

  const handleEmailClick = () => {
    const email = organization?.email || '<EMAIL>';
    window.open(`mailto:${email}`, '_self');
  };

  const handleWhatsAppClick = () => {
    const phoneNumber = organization?.contact_number || '919876543210';
    window.open(`https://wa.me/${phoneNumber}?text=Hi, I would like to know more about study abroad opportunities.`, '_blank');
  };

  const handleLocationClick = () => {
    const organizationName = organization?.name || 'NextGen Hub Education Consultants';
    window.open(`https://maps.google.com?q=${encodeURIComponent(organizationName)}`, '_blank');
  };

  if (isLoading) {
    return (
      <section id="contact" className="py-20 bg-white dark:bg-gray-900 transition-colors">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Loading contact information...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-20 bg-white dark:bg-gray-900 transition-colors">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Get in Touch
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Ready to start your study abroad journey? Contact us today for expert guidance.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Contact Information</h3>
              <div className="space-y-4">
                <div 
                  onClick={handleCallClick}
                  className="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition-all duration-200 cursor-pointer group border border-gray-200 dark:border-gray-700"
                >
                  <div className="bg-primary/10 dark:bg-primary/20 group-hover:bg-primary group-hover:text-white w-12 h-12 rounded-full flex items-center justify-center mr-4 transition-all duration-200">
                    <svg className="w-6 h-6 text-primary dark:text-primary group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">Phone</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {organization?.contact_number || '+91 98765 43210'}
                    </p>
                  </div>
                </div>

                <div 
                  onClick={handleEmailClick}
                  className="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition-all duration-200 cursor-pointer group border border-gray-200 dark:border-gray-700"
                >
                  <div className="bg-primary/10 dark:bg-primary/20 group-hover:bg-primary group-hover:text-white w-12 h-12 rounded-full flex items-center justify-center mr-4 transition-all duration-200">
                    <svg className="w-6 h-6 text-primary dark:text-primary group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">Email</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {organization?.email || '<EMAIL>'}
                    </p>
                  </div>
                </div>

                <div 
                  onClick={handleLocationClick}
                  className="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition-all duration-200 cursor-pointer group border border-gray-200 dark:border-gray-700"
                >
                  <div className="bg-primary/10 dark:bg-primary/20 group-hover:bg-primary group-hover:text-white w-12 h-12 rounded-full flex items-center justify-center mr-4 transition-all duration-200">
                    <svg className="w-6 h-6 text-primary dark:text-primary group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">Office</p>
                    <p className="text-gray-600 dark:text-gray-300">
                      {organization?.address || '123 Education Street, City Center'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="bg-primary/10 dark:bg-primary/20 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-primary dark:text-primary" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">Working Hours</p>
                    <p className="text-gray-600 dark:text-gray-300">Mon-Fri: 9 AM - 7 PM</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <Button 
                onClick={handleWhatsAppClick}
                className="bg-green-500 hover:bg-green-600 text-white flex items-center transform hover:scale-105 transition-all duration-200"
              >
                <MessageCircle size={20} />
                <span className="ml-2">WhatsApp</span>
              </Button>
              <Button 
                onClick={handleCallClick}
                variant="outline"
                className="border-primary text-primary hover:bg-primary hover:text-white dark:border-primary dark:text-primary dark:hover:bg-primary dark:hover:text-white transform hover:scale-105 transition-all duration-200"
              >
                Call Now
              </Button>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-700">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send us a Message</h3>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="full_name"
                    value={contactForm.full_name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                    placeholder="Your full name"
                    disabled={isSubmitting}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={contactForm.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                    placeholder="<EMAIL>"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Contact Number
                  </label>
                  <input
                    type="tel"
                    name="contact_number"
                    value={contactForm.contact_number}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                    placeholder="+91 9876543210"
                    disabled={isSubmitting}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Service
                  </label>
                  <select
                    name="service"
                    value={contactForm.service}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                    disabled={isSubmitting}
                  >
                    <option value="" className="text-gray-500 dark:text-gray-400">Select a service</option>
                    {services.map((service) => (
                      <option 
                        key={service.id} 
                        value={service.id.toString()} 
                        className="text-gray-900 dark:text-white"
                      >
                        {service.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Message *
                </label>
                <textarea
                  name="message"
                  value={contactForm.message}
                  onChange={handleChange}
                  required
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
                  placeholder="Tell us how we can help you..."
                  disabled={isSubmitting}
                />
              </div>

              <Button 
                type="submit"
                size="lg"
                className="w-full bg-primary hover:bg-primary/90 text-white transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  'Send Message'
                )}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;