import React, { useState, useEffect } from 'react';
import { motion, easeOut } from 'framer-motion';
import { MediaImage } from '@/components/ui/media-image';
import { getExperiences, getMediaUrl } from './APis/ApiService';
import type { Experience } from '@/types/api';

const AboutSection = () => {
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchExperiences = async () => {
      try {
        const data = await getExperiences();
        setExperiences(data);
      } catch (error) {
    
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easeOut
      }
    }
  };

  const cardHoverVariants = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.05,
      y: -8,
      transition: {
        duration: 0.3,
        ease: easeOut
      }
    }
  };

  const iconVariants = {
    rest: { rotate: 0, scale: 1 },
    hover: {
      rotate: 360,
      scale: 1.1,
      transition: {
        duration: 0.6,
        ease: easeOut
      }
    }
  };

  return (
    <>
      <style>{`
        .experience-content p {
          margin-bottom: 1rem;
          line-height: 1.6;
        }
        
        .experience-content ul {
          margin-top: 1rem;
          list-style: none;
          padding-left: 0;
        }
        
        .experience-content li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 0.5rem;
          position: relative;
          padding-left: 1.5rem;
        }
        
        .experience-content li::before {
          content: "✓";
          color: #10b981;
          font-weight: bold;
          position: absolute;
          left: 0;
          top: 0.125rem;
          flex-shrink: 0;
        }
        
        .experience-content li p {
          margin-bottom: 0;
          margin-left: 0;
        }
        
        .experience-content span {
          /* Preserve any inline styles from CKEditor */
        }
      `}</style>
      <section id="about" className="py-20 bg-white dark:bg-gray-900 transition-colors">
        <div className="container mx-auto px-4">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#2563eb' }}>
              Why Choose <span style={{ color: '#ef4444' }}>Next Gen Hub ?</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              With years of experience and a proven track record, we're your trusted partner in making your study abroad dreams come true.
            </p>
          </motion.div>

          <motion.div 
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#2563eb20' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#2563eb' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Expert Guidance</h3>
              <p className="text-gray-600 dark:text-gray-300">Certified counselors with deep knowledge of international education systems</p>
            </motion.div>

            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#ef444420' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#ef4444' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Comprehensive Services</h3>
              <p className="text-gray-600 dark:text-gray-300">End-to-end support from university selection to visa approval</p>
            </motion.div>

            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#2563eb20' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#2563eb' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Global Network</h3>
              <p className="text-gray-600 dark:text-gray-300">Strong partnerships with universities worldwide for better opportunities</p>
            </motion.div>

            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#ef444420' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#ef4444' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Success Rate</h3>
              <p className="text-gray-600 dark:text-gray-300">95% visa success rate with thousands of satisfied students</p>
            </motion.div>
          </motion.div>

          {/* Dynamic Experience Section */}
          
        </div>
      </section>
    </>
  );
};

export default AboutSection;