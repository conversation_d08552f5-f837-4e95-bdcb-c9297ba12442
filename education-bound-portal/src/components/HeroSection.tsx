import { Button } from '@/components/ui/button';
import { MediaImage, MediaVideo } from '@/components/ui/media-image';
import { getHeroes, getMediaUrl } from '@/components/APis/ApiService';
import { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Volume2, VolumeX, Play, Pause } from 'lucide-react';
import type { Hero } from '@/types/api';

const getHeroSection = async (): Promise<Hero | null> => {
  try {
    const data = await getHeroes();
    if (data && Array.isArray(data) && data.length > 0) {
      return data[0];
    }
    return null;
  } catch (error) {
    
    return null;
  }
};

const HeroSection = () => {
  const [heroData, setHeroData] = useState<Hero | null>(null);
  const [loading, setLoading] = useState(true);
  const [showVideo, setShowVideo] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [isPlaying, setIsPlaying] = useState(true); // Start as true for autoplay
  const [isVideoInView, setIsVideoInView] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const fetchHeroData = async () => {
      setLoading(true);
      const data = await getHeroSection();
      setHeroData(data);
      setLoading(false);
    };

    fetchHeroData();
  }, []);

  // Trigger video transition after 1.5 seconds (from first version)
  useEffect(() => {
    if (!loading) {
      const timer = setTimeout(() => {
        setShowVideo(true);
      }, 1500); // 1.5 seconds - optimal impression time

      return () => clearTimeout(timer);
    }
  }, [loading]);

  // Intersection Observer for video visibility (simplified from first version)
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVideoInView(entry.isIntersecting);
        
        // Auto pause/play video when out of view
        if (videoRef.current) {
          if (entry.isIntersecting) {
            if (isPlaying) {
              videoRef.current.play().catch(() => {});
            }
          } else {
            videoRef.current.pause();
          }
        }
      },
      { threshold: 0.3 } // Trigger when 30% of video is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isPlaying]);

  // Handle video controls (improved from second version)
  const toggleMute = () => {
    if (videoRef.current) {
      const newMutedState = !isMuted;
      videoRef.current.muted = newMutedState;
      setIsMuted(newMutedState);
    }
  };

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        videoRef.current.play()
          .then(() => setIsPlaying(true))
          .catch((error) => {
           
            setIsPlaying(false);
          });
      }
    }
  };

  const scrollToBooking = () => {
    const element = document.getElementById('booking');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToAbout = () => {
    const element = document.getElementById('about');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Default fallback data
  const defaultData = {
    title: "Your Study Abroad Journey Starts Here",
    description: "Expert guidance for your international education dreams. From university selection to visa processing, we're with you every step of the way.",
    image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=1200&h=800&fit=crop",
    video: "https://videos.pexels.com/video-files/3196386/3196386-uhd_2560_1440_30fps.mp4"
  };

  const displayData = heroData ? {
    ...heroData,
    image: getMediaUrl(heroData.image) || defaultData.image,
    video: getMediaUrl(heroData.video) || defaultData.video
  } : defaultData;

  // Parse title to identify colored parts
  const parseTitle = (title: string) => {
    if (title.includes("Journey") && title.includes("Starts Here")) {
      const parts = title.split(/(\bJourney\b|\bStarts Here\b)/);
      return parts.map((part, index) => {
        if (part === "Journey") {
          return <span key={index} className="text-primary">{part}</span>;
        } else if (part === "Starts Here") {
          return <span key={index} className="text-accent">{part}</span>;
        }
        return part;
      });
    }
    
    const words = title.trim().split(' ');
    if (words.length >= 3) {
      const beforeLast = words.slice(0, -2).join(' ');
      const secondLast = words[words.length - 2];
      const last = words[words.length - 1];
      
      return (
        <>
          {beforeLast + ' '}
          <span className="text-primary">{secondLast}</span>
          {' '}
          <span className="text-accent">{last}</span>
        </>
      );
    }
    
    return title;
  };

  if (loading) {
    return (
      <section id="hero" className="pt-16 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20 min-h-screen flex items-center transition-colors">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="animate-pulse">
              <div className="h-16 bg-gray-300 dark:bg-gray-700 rounded mb-6"></div>
              <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded mb-8"></div>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="h-12 bg-gray-300 dark:bg-gray-700 rounded"></div>
                <div className="h-12 bg-gray-300 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            <div className="animate-pulse">
              <div className="h-80 bg-gray-300 dark:bg-gray-700 rounded-lg"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={sectionRef} id="hero" className="relative min-h-screen overflow-hidden">
      <AnimatePresence mode="wait">
        {!showVideo ? (
          // Initial Static Content
          <motion.div
            key="static-content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8 }}
            className="pt-16 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20 min-h-screen flex items-center transition-colors"
          >
            <div className="container mx-auto px-4 relative z-10">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Left Content */}
                <motion.div
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                    {parseTitle(displayData.title)}
                  </h1>
                  <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                    {displayData.description}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button 
                      size="lg" 
                      
                      onClick={scrollToBooking}
                      className="bg-primary hover:bg-primary/90 text-white px-8 py-4 text-lg"
                    >
                      Book Free Counseling
                    </Button>
                    <Button 
                      variant="outline" 
                      size="lg"
                      onClick={scrollToAbout}
                      className="border-primary text-primary hover:bg-primary hover:text-white dark:border-primary dark:text-primary dark:hover:bg-primary dark:hover:text-white px-8 py-4 text-lg"
                    >
                      Learn More
                    </Button>
                  </div>
                  <div className="mt-8 flex items-center space-x-8">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">500+</div>
                      <div className="text-gray-600 dark:text-gray-400">Success Stories</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-accent">95%</div>
                      <div className="text-gray-600 dark:text-gray-400">Visa Success Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">10+</div>
                      <div className="text-gray-600 dark:text-gray-400">Countries</div>
                    </div>
                  </div>
                </motion.div>

                {/* Right Image */}
                <motion.div
                  initial={{ x: 50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                    <MediaImage
                      src={heroData?.image || null}
                      alt="Students studying abroad"
                      className="w-full h-80 lg:h-96 object-cover"
                      fallbackSrc="https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=1200&h=800&fit=crop"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        ) : (
          // Full Screen Video with Overlay CTAs
          <motion.div
            key="video-content"
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1.2, ease: "easeOut" }}
            className="relative min-h-screen"
          >
            {/* Full Screen Video Background with Proper Aspect Ratio */}
            <div className="absolute inset-0">
              {/* Video Container with Aspect Ratio Control */}
              <div className="relative w-full h-full bg-black">
                <video 
                  ref={videoRef}
                  className="absolute inset-0 w-full h-full object-cover md:object-contain lg:object-cover"
                  style={{
                    aspectRatio: '16/9'
                  }}
                  autoPlay // Keep autoPlay for initial start
                  muted={isMuted}
                  loop
                  playsInline
                  preload="metadata"
                  onLoadedData={() => {
                    // Ensure video plays when loaded
                    if (videoRef.current && isVideoInView) {
                      videoRef.current.play().catch(() => {});
                    }
                  }}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  
                >
                  <source src={displayData.video} type="video/mp4" />
                  {/* Fallback to image if video fails */}
                  <MediaImage
                    src={heroData?.image || null}
                    alt="Students studying abroad"
                    className="w-full h-full object-cover"
                    fallbackSrc="https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=1200&h=800&fit=crop"
                  />
                </video>
              </div>
              
              {/* Dark overlay for better text readability */}
              <div className="absolute inset-0 bg-black/30"></div>
            </div>

            {/* Video Controls - Bottom Left (improved positioning) */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="absolute bottom-20 left-6 md:left-12 z-20 flex gap-3"
            >
              <button
                onClick={toggleMute}
                className="bg-black/60 hover:bg-black/80 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
                aria-label={isMuted ? "Unmute video" : "Mute video"}
              >
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </button>
              <button
                onClick={togglePlayPause}
                className="bg-black/60 hover:bg-black/80 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
                aria-label={isPlaying ? "Pause video" : "Play video"}
              >
                {isPlaying ? <Pause size={20} /> : <Play size={20} />}
              </button>
            </motion.div>

            {/* Video Status Indicator - Top Left (only when out of view) */}
            {!isVideoInView && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute top-6 left-6 z-20"
              >
                <div className="bg-black/60 text-white px-3 py-2 rounded-full backdrop-blur-sm text-sm">
                  Video Paused (Out of View)
                </div>
              </motion.div>
            )}

            {/* CTA Overlay - Positioned to complement video, not compete */}
            <div className="relative z-10 min-h-screen">
              
              {/* Subtle top-left branding/tagline */}
              <motion.div
                initial={{ x: -30, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="absolute top-24 left-6 md:left-12"
              >
                <h2 className="text-xl md:text-2xl font-bold text-white drop-shadow-lg">
                  Ready to Start Your
                  <span className="block text-accent">Global Education?</span>
                </h2>
              </motion.div>

              {/* Main CTA Area - Bottom positioning for all screens */}
              <div className="absolute bottom-0 left-0 right-0">
                {/* Trust indicators bar - top of CTA area */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                  className="mb-6 px-6 md:px-12"
                >
                  <div className="flex items-center justify-center space-x-6 md:space-x-12 text-white/90 backdrop-blur-sm bg-black/20 rounded-full py-3 px-6 max-w-fit mx-auto">
                    <div className="text-center">
                      <div className="text-lg md:text-xl font-bold text-accent drop-shadow-lg">500+</div>
                      <div className="text-xs md:text-sm">Success Stories</div>
                    </div>
                    <div className="w-px h-8 bg-white/30"></div>
                    <div className="text-center">
                      <div className="text-lg md:text-xl font-bold text-accent drop-shadow-lg">95%</div>
                      <div className="text-xs md:text-sm">Visa Success</div>
                    </div>
                    <div className="w-px h-8 bg-white/30"></div>
                    <div className="text-center">
                      <div className="text-lg md:text-xl font-bold text-accent drop-shadow-lg">10+</div>
                      <div className="text-xs md:text-sm">Countries</div>
                    </div>
                  </div>
                </motion.div>

                {/* Primary CTA Buttons */}
                <motion.div
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                  className="px-6 md:px-12 pb-8 md:pb-12"
                >
                  {/* Desktop: Bottom-right corner positioning */}
                  <div className="hidden md:flex justify-end">
                    <div className="flex flex-col gap-3 max-w-xs">
                      <Button 
                        size="lg" 
                        onClick={scrollToBooking}
                        className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-base font-semibold shadow-2xl hover:shadow-primary/50 transition-all duration-300 transform hover:scale-105 w-full"
                      >
                        Book Free Counseling
                      </Button>
                      <Button 
                        variant="outline" 
                        size="lg"
                        onClick={scrollToAbout}
                        className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 text-base font-semibold backdrop-blur-sm bg-white/10 transition-all duration-300 transform hover:scale-105 w-full"
                      >
                        Learn More
                      </Button>
                    </div>
                  </div>

                  {/* Mobile: Bottom-center with safe area */}
                  <div className="md:hidden flex justify-center">
                    <div className="flex flex-col gap-3 w-full max-w-sm">
                      <Button 
                        size="lg" 
                        onClick={scrollToBooking}
                        className="bg-primary hover:bg-primary/90 text-white px-8 py-4 text-lg font-semibold shadow-2xl hover:shadow-primary/50 transition-all duration-300 transform hover:scale-105 w-full"
                      >
                        Book Free Counseling
                      </Button>
                      <Button 
                        variant="outline" 
                        size="lg"
                        onClick={scrollToAbout}
                        className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold backdrop-blur-sm bg-white/10 transition-all duration-300 transform hover:scale-105 w-full"
                      >
                        Learn More
                      </Button>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Optional: Skip to content button with video status indicator */}
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2 }}
              onClick={scrollToAbout}
              className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/70 hover:text-white transition-colors"
              aria-label="Skip to main content"
            >
              <div className="flex flex-col max-w-[767px]:hidden items-center space-y-2">
                <div className="text-sm flex items-center gap-2">
                  <span>Scroll to explore</span>
                  {!isVideoInView && (
                    <span className="text-xs bg-white/20 px-2 py-1 rounded-full">
                      Video paused
                    </span>
                  )}
                </div>
                <motion.div
                  animate={{ y: [0, 8, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
                >
                  <div className="w-1 h-3 bg-white/70 rounded-full mt-2"></div>
                </motion.div>
              </div>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default HeroSection;